#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
港股通持股比例变化分析工具

专门用于分析港股通持股比例的变化情况，找出持股比例增加最大的公司。
"""

import sys
import os
import pandas as pd
from datetime import datetime

# --- Path Setup ---
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入交易日历工具
from utilities.utils import get_futu_trading_calendar

def print_beautiful_table(df: pd.DataFrame, title: str = "数据表格"):
    """
    美化打印DataFrame表格，使用ASCII字符确保兼容性

    Args:
        df: 要打印的DataFrame
        title: 表格标题
    """
    if df.empty:
        print(f"📊 {title}: 暂无数据")
        return

    # 计算每列的最大宽度
    col_widths = {}
    for col in df.columns:
        # 计算列名和数据的最大宽度
        col_name_width = len(str(col))
        data_width = df[col].astype(str).str.len().max() if not df[col].empty else 0
        col_widths[col] = max(col_name_width, data_width, 8)  # 最小宽度为8

    # 打印表格标题
    total_width = sum(col_widths.values()) + len(col_widths) * 3 + 1
    print(f"\n📊 {title}")
    print("+" + "=" * (total_width - 2) + "+")

    # 打印表头
    header_line = "|"
    separator_line = "+"

    for i, col in enumerate(df.columns):
        width = col_widths[col]
        header_line += f" {str(col):^{width}} |"
        separator_line += "=" * (width + 2) + "+"

    print(header_line)
    print(separator_line)

    # 打印数据行
    for idx, row in df.iterrows():
        data_line = "|"
        for col in df.columns:
            width = col_widths[col]
            value = str(row[col])

            # 根据数据类型调整对齐方式
            if col in ['股票代码', '股票名称', '最新日期']:
                # 文本类型居中对齐
                data_line += f" {value:^{width}} |"
            else:
                # 数值类型右对齐
                data_line += f" {value:>{width}} |"

        print(data_line)

    # 打印表格底部
    print("+" + "=" * (total_width - 2) + "+")

    # 添加排名信息
    print(f"💡 显示前 {len(df)} 名结果")

    # 如果有持股比例变化数据，显示统计信息
    if '持股比例变化' in df.columns:
        avg_change = df['持股比例变化'].mean()
        max_change = df['持股比例变化'].max()
        min_change = df['持股比例变化'].min()
        print(f"📈 平均变化: {avg_change:.2f}% | 最大变化: {max_change:.2f}% | 最小变化: {min_change:.2f}%")

    print()

def analyze_hsgt_holdings_change(df: pd.DataFrame, trading_days: int = 5) -> pd.DataFrame:
    """
    分析港股通持股比例变化，找出最近N个交易日增加最大的公司

    Args:
        df: 港股数据DataFrame
        trading_days: 分析的交易日数，默认5个交易日

    Returns:
        DataFrame: 包含持股比例变化分析结果的数据框
    """
    print(f"分析最近{trading_days}个交易日港股通持股比例变化...")

    # 过滤有港股通数据的记录
    hsgt_data = df[df['hsgt_holding_ratio'].notna()].copy()

    if hsgt_data.empty:
        print("未找到港股通持股数据")
        return pd.DataFrame()

    # 确保time_key是datetime类型
    hsgt_data['time_key'] = pd.to_datetime(hsgt_data['time_key'])

    # 获取所有有数据的交易日，并按日期排序
    all_trading_dates = sorted(hsgt_data['time_key'].dt.date.unique())

    if len(all_trading_dates) < trading_days:
        print(f"警告: 数据中只有{len(all_trading_dates)}个交易日，少于请求的{trading_days}个交易日")
        trading_days = len(all_trading_dates)

    # 获取最近N个交易日
    recent_trading_dates = all_trading_dates[-trading_days:]
    start_date = pd.Timestamp(recent_trading_dates[0])
    latest_date = pd.Timestamp(recent_trading_dates[-1])

    print(f"分析时间范围: {start_date.strftime('%Y-%m-%d')} 至 {latest_date.strftime('%Y-%m-%d')} (共{trading_days}个交易日)")

    # 筛选最近N个交易日的数据
    recent_data = hsgt_data[hsgt_data['time_key'].dt.date.isin(recent_trading_dates)].copy()
    
    results = []
    for code in recent_data['code'].unique():
        stock_data = recent_data[recent_data['code'] == code].sort_values('time_key')
        
        if len(stock_data) < 2:
            continue
            
        # 计算持股比例变化
        latest_ratio = stock_data['hsgt_holding_ratio'].iloc[-1]
        earliest_ratio = stock_data['hsgt_holding_ratio'].iloc[0]
        
        if pd.notna(latest_ratio) and pd.notna(earliest_ratio):
            ratio_change = latest_ratio - earliest_ratio
            
            results.append({
                'code': code,
                'name': stock_data['name'].iloc[-1],
                'latest_date': stock_data['time_key'].iloc[-1].strftime('%Y-%m-%d'),
                'earliest_date': stock_data['time_key'].iloc[0].strftime('%Y-%m-%d'),
                'earliest_ratio': earliest_ratio,
                'latest_ratio': latest_ratio,
                'ratio_change': ratio_change,
                'ratio_change_pct': (ratio_change / earliest_ratio * 100) if earliest_ratio > 0 else 0,
                'latest_holding_value': stock_data['hsgt_holding_value'].iloc[-1] if 'hsgt_holding_value' in stock_data.columns else None,
                'latest_close': stock_data['close'].iloc[-1],
                'data_points': len(stock_data)
            })
    
    if not results:
        print("未找到有效的港股通持股比例变化数据")
        return pd.DataFrame()
    
    # 转换为DataFrame并按持股比例变化排序
    result_df = pd.DataFrame(results)
    result_df = result_df.sort_values('ratio_change', ascending=False)
    
    return result_df

def check_hsgt_data_coverage(df: pd.DataFrame):
    """检查港股通数据覆盖情况"""
    print("="*80)
    print("           港股通数据覆盖情况检查")
    print("="*80)

    # 统计总体情况
    total_records = len(df)
    total_stocks = df['code'].nunique()

    # 检查hsgt_holding_ratio字段
    has_hsgt_ratio = df['hsgt_holding_ratio'].notna()
    records_with_hsgt = has_hsgt_ratio.sum()
    stocks_with_hsgt = df[has_hsgt_ratio]['code'].nunique()

    print(f"📊 数据总览:")
    print(f"   总记录数: {total_records:,}")
    print(f"   总股票数: {total_stocks}")
    print(f"   有港股通持股比例数据的记录: {records_with_hsgt:,} ({records_with_hsgt/total_records*100:.1f}%)")
    print(f"   有港股通持股比例数据的股票: {stocks_with_hsgt} ({stocks_with_hsgt/total_stocks*100:.1f}%)")

    # 按股票统计港股通数据覆盖情况
    stock_coverage = []
    for code in df['code'].unique():
        stock_data = df[df['code'] == code]
        stock_name = stock_data['name'].iloc[0]
        total_days = len(stock_data)
        hsgt_days = stock_data['hsgt_holding_ratio'].notna().sum()
        coverage_pct = (hsgt_days / total_days * 100) if total_days > 0 else 0

        # 获取最新和最早的港股通数据日期
        hsgt_data = stock_data[stock_data['hsgt_holding_ratio'].notna()]
        if not hsgt_data.empty:
            latest_hsgt_date = hsgt_data['time_key'].max()
            earliest_hsgt_date = hsgt_data['time_key'].min()
        else:
            latest_hsgt_date = None
            earliest_hsgt_date = None

        stock_coverage.append({
            'code': code,
            'name': stock_name,
            'total_days': total_days,
            'hsgt_days': hsgt_days,
            'coverage_pct': coverage_pct,
            'latest_hsgt_date': latest_hsgt_date,
            'earliest_hsgt_date': earliest_hsgt_date
        })

    coverage_df = pd.DataFrame(stock_coverage)

    # 显示没有港股通数据的股票
    no_hsgt_stocks = coverage_df[coverage_df['hsgt_days'] == 0]
    print(f"\n❌ 完全没有港股通数据的股票 ({len(no_hsgt_stocks)} 只):")
    if not no_hsgt_stocks.empty:
        for _, row in no_hsgt_stocks.head(10).iterrows():
            print(f"   {row['code']} - {row['name']} (共{row['total_days']}天数据)")
        if len(no_hsgt_stocks) > 10:
            print(f"   ... 还有 {len(no_hsgt_stocks) - 10} 只股票")

    return coverage_df

def main():
    """主函数"""
    print("="*80)
    print("           港股通持股比例变化分析工具")
    print("="*80)

    # 数据文件路径
    data_path = os.path.join(project_root, 'data', 'h_shares_daily.parquet')

    if not os.path.exists(data_path):
        print(f"错误: 未找到数据文件 {data_path}")
        return

    print(f"正在加载数据: {data_path}")
    try:
        df = pd.read_parquet(data_path)
        print(f"数据加载成功，共 {len(df)} 条记录")
    except Exception as e:
        print(f"数据加载失败: {e}")
        return

    # 首先检查港股通数据覆盖情况
    coverage_df = check_hsgt_data_coverage(df)

    # 分析港股通持股比例变化
    analysis_trading_days = 2  # 可以根据需要调整，这里是2个交易日
    hsgt_analysis = analyze_hsgt_holdings_change(df, trading_days=analysis_trading_days)

    if hsgt_analysis.empty:
        print("未找到港股通持股比例变化数据")
        return
    
    # 显示结果 - 持股比例增加最多的
    print(f"\n🔥 港股通持股比例增加最大的10家公司 (最近{analysis_trading_days}个交易日):")
    print("="*120)

    # 显示前10名
    top_10_increase = hsgt_analysis.head(10)

    # 格式化显示
    display_cols = ['code', 'name', 'latest_date', 'earliest_ratio', 'latest_ratio',
                   'ratio_change', 'ratio_change_pct', 'latest_close']

    formatted_increase = top_10_increase[display_cols].copy()
    formatted_increase['earliest_ratio'] = formatted_increase['earliest_ratio'].round(2)
    formatted_increase['latest_ratio'] = formatted_increase['latest_ratio'].round(2)
    formatted_increase['ratio_change'] = formatted_increase['ratio_change'].round(2)
    formatted_increase['ratio_change_pct'] = formatted_increase['ratio_change_pct'].round(2)
    formatted_increase['latest_close'] = formatted_increase['latest_close'].round(2)

    # 重命名列名为中文
    formatted_increase.columns = ['股票代码', '股票名称', '最新日期', '期初持股比例(%)',
                                 '最新持股比例(%)', '持股比例变化', '变化百分比(%)', '最新收盘价']

    # 美化表格输出
    print_beautiful_table(formatted_increase, "港股通持股比例增加最大的公司")

    # 显示结果 - 持股比例减少最多的
    print(f"\n📉 港股通持股比例减少最大的10家公司 (最近{analysis_trading_days}个交易日):")
    print("="*120)

    # 显示后10名（减少最多的）
    bottom_10_decrease = hsgt_analysis.tail(10).iloc[::-1]  # 取最后10个并反转顺序

    formatted_decrease = bottom_10_decrease[display_cols].copy()
    formatted_decrease['earliest_ratio'] = formatted_decrease['earliest_ratio'].round(2)
    formatted_decrease['latest_ratio'] = formatted_decrease['latest_ratio'].round(2)
    formatted_decrease['ratio_change'] = formatted_decrease['ratio_change'].round(2)
    formatted_decrease['ratio_change_pct'] = formatted_decrease['ratio_change_pct'].round(2)
    formatted_decrease['latest_close'] = formatted_decrease['latest_close'].round(2)

    # 重命名列名为中文
    formatted_decrease.columns = ['股票代码', '股票名称', '最新日期', '期初持股比例(%)',
                                 '最新持股比例(%)', '持股比例变化', '变化百分比(%)', '最新收盘价']

    # 美化表格输出
    print_beautiful_table(formatted_decrease, "港股通持股比例减少最大的公司")
    
    # 统计信息
    total_increase_count = len(hsgt_analysis[hsgt_analysis['ratio_change'] > 0])
    total_decrease_count = len(hsgt_analysis[hsgt_analysis['ratio_change'] < 0])
    avg_change = hsgt_analysis['ratio_change'].mean()
    max_increase = hsgt_analysis['ratio_change'].max()
    max_decrease = hsgt_analysis['ratio_change'].min()
    
    print(f"\n📊 统计信息:")
    print(f"   分析公司总数: {len(hsgt_analysis)} 家")
    print(f"   持股比例增加的公司: {total_increase_count} 家")
    print(f"   持股比例减少的公司: {total_decrease_count} 家")
    print(f"   平均持股比例变化: {avg_change:.2f}%")
    print(f"   最大增幅: {max_increase:.2f}%")
    print(f"   最大减幅: {max_decrease:.2f}%")
    
    # 保存结果
    output_dir = os.path.join(os.path.dirname(__file__), 'backtest_results')
    os.makedirs(output_dir, exist_ok=True)
    
    current_date = datetime.now().strftime('%Y%m%d_%H%M%S')
    csv_filename = f'hsgt_holdings_analysis_{current_date}.csv'
    csv_path = os.path.join(output_dir, csv_filename)
    
    hsgt_analysis.to_csv(csv_path, index=False, encoding='utf-8-sig')
    print(f"\n💾 完整分析结果已保存到: {csv_path}")
    print(f"   (包含所有 {len(hsgt_analysis)} 家公司的详细数据)")
    
    print("="*80)

if __name__ == "__main__":
    main()
