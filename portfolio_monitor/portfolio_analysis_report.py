#!/usr/bin/env python3
"""
投资组合分析报告生成器
基于最新股价数据生成详细的投资组合报告
"""

import os
import sys
import pandas as pd
import numpy as np
import warnings
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import matplotlib.pyplot as plt
import seaborn as sns

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utilities.utils import get_last_futu_trading_day, load_stock_data
from utilities.complete_font_solution import setup_chinese_fonts
from portfolio_monitor.real_time_price_fetcher import RealTimePriceFetcher

warnings.filterwarnings('ignore')

class PortfolioAnalyzer:
    """投资组合分析器"""
    
    def __init__(self, portfolio_file: str, data_dir: str = "data/H_daily", use_real_time_prices: bool = True):
        """
        初始化投资组合分析器

        Args:
            portfolio_file: 投资组合文件路径
            data_dir: 股票数据目录
            use_real_time_prices: 是否使用实时价格计算投资组合价值
        """
        self.portfolio_file = portfolio_file
        self.data_dir = data_dir
        self.use_real_time_prices = use_real_time_prices
        self.portfolio_data = None
        self.stock_data = {}
        self.latest_prices = {}
        self.real_time_prices = {}
        self.analysis_results = {}

        # 设置中文字体
        setup_chinese_fonts()

        # 初始化实时价格获取器
        self.price_fetcher = None
        if use_real_time_prices:
            self.price_fetcher = RealTimePriceFetcher()
        
    def load_portfolio(self) -> pd.DataFrame:
        """加载投资组合数据"""
        try:
            # 使用正确的分隔符读取文件
            portfolio_df = pd.read_csv(self.portfolio_file, sep=r'\s+', engine='python')

            # 检查必要的列是否存在
            if 'code' not in portfolio_df.columns or 'shares_held' not in portfolio_df.columns:
                print(f"❌ 投资组合文件格式错误，缺少必要的列: {portfolio_df.columns.tolist()}")
                return None

            # 清理股票代码，去掉HK.前缀
            portfolio_df['code_clean'] = portfolio_df['code'].str.replace('HK.', '', regex=False)
            self.portfolio_data = portfolio_df
            print(f"✅ 成功加载投资组合，共 {len(portfolio_df)} 只股票")
            print(f"📋 投资组合股票: {portfolio_df['code'].tolist()}")
            return portfolio_df
        except Exception as e:
            print(f"❌ 加载投资组合失败: {e}")
            # 尝试手动读取文件内容进行调试
            try:
                with open(self.portfolio_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    print(f"📄 文件内容预览:\n{content[:200]}")
            except:
                pass
            return None
    
    def load_stock_data_for_portfolio(self) -> Dict:
        """为投资组合中的所有股票加载数据"""
        if self.portfolio_data is None:
            print("❌ 请先加载投资组合数据")
            return {}
        
        stock_data = {}
        for _, row in self.portfolio_data.iterrows():
            stock_code = row['code_clean']
            file_path = os.path.join(self.data_dir, f"{stock_code}.csv")
            
            if os.path.exists(file_path):
                try:
                    df = pd.read_csv(file_path)
                    df['time_key'] = pd.to_datetime(df['time_key'])
                    df = df.sort_values('time_key').reset_index(drop=True)
                    stock_data[stock_code] = df
                    print(f"✅ 加载 {stock_code} ({row['code']}) 数据成功")
                except Exception as e:
                    print(f"❌ 加载 {stock_code} 数据失败: {e}")
            else:
                print(f"⚠️  未找到 {stock_code} 的数据文件")
        
        self.stock_data = stock_data
        return stock_data
    
    def get_latest_prices_and_info(self) -> Dict:
        """获取最新价格和基本信息"""
        latest_info = {}
        
        for stock_code, df in self.stock_data.items():
            if df.empty:
                continue
                
            latest_row = df.iloc[-1]
            latest_info[stock_code] = {
                'latest_price': latest_row['close'],
                'latest_date': latest_row['time_key'],
                'stock_name': latest_row['name'],
                'pe_ratio': latest_row.get('pe_ratio', np.nan),
                'turnover_rate': latest_row.get('turnover_rate', np.nan),
                'volume': latest_row.get('volume', np.nan),
                'change_rate': latest_row.get('change_rate', np.nan)
            }
        
        self.latest_prices = latest_info
        return latest_info

    def get_real_time_prices(self) -> Dict:
        """获取实时价格数据"""
        if not self.use_real_time_prices or not self.price_fetcher:
            print("⚠️  未启用实时价格获取，使用历史数据")
            return {}

        if self.portfolio_data is None:
            print("❌ 请先加载投资组合数据")
            return {}

        try:
            # 连接富途API
            if not self.price_fetcher.connect():
                print("❌ 无法连接富途API，使用历史数据")
                return {}

            # 获取股票代码列表
            stock_codes = self.portfolio_data['code_clean'].tolist()

            # 批量获取实时价格
            real_time_data = self.price_fetcher.get_batch_real_time_prices(stock_codes)

            # 断开连接
            self.price_fetcher.disconnect()

            self.real_time_prices = real_time_data
            print(f"✅ 成功获取 {len(real_time_data)} 只股票的实时价格")
            return real_time_data

        except Exception as e:
            print(f"❌ 获取实时价格失败: {e}")
            return {}
    
    def calculate_returns_and_volatility(self, periods: List[int] = [5, 20, 60, 200]) -> Dict:
        """计算不同周期的收益率和波动率"""
        returns_data = {}
        
        for stock_code, df in self.stock_data.items():
            if df.empty or len(df) < max(periods):
                continue
            
            stock_returns = {}
            
            # 计算不同周期的收益率
            for period in periods:
                if len(df) >= period:
                    current_price = df['close'].iloc[-1]
                    past_price = df['close'].iloc[-period-1]
                    period_return = (current_price - past_price) / past_price * 100
                    stock_returns[f'return_{period}d'] = period_return
            
            # 计算波动率
            df['daily_return'] = df['close'].pct_change()
            
            # 当前波动率（20日）
            current_volatility = df['daily_return'].tail(20).std() * np.sqrt(252) * 100
            stock_returns['current_volatility'] = current_volatility
            
            # 历史波动率分位数
            if len(df) >= 252:  # 至少一年数据
                rolling_vol = df['daily_return'].rolling(20).std() * np.sqrt(252) * 100
                rolling_vol = rolling_vol.dropna()
                if len(rolling_vol) > 0:
                    vol_percentile = (rolling_vol <= current_volatility).mean() * 100
                    stock_returns['volatility_percentile'] = vol_percentile
            
            returns_data[stock_code] = stock_returns
        
        return returns_data
    
    def calculate_pe_percentiles(self) -> Dict:
        """计算PE历史分位数"""
        pe_data = {}
        
        for stock_code, df in self.stock_data.items():
            if df.empty:
                continue
            
            pe_series = df['pe_ratio'].dropna()
            if len(pe_series) == 0:
                continue
            
            current_pe = pe_series.iloc[-1]
            if pd.isna(current_pe) or current_pe <= 0:
                continue
            
            # 计算历史分位数
            pe_percentile = (pe_series <= current_pe).mean() * 100
            
            pe_data[stock_code] = {
                'current_pe': current_pe,
                'pe_percentile': pe_percentile,
                'pe_min': pe_series.min(),
                'pe_max': pe_series.max(),
                'pe_median': pe_series.median()
            }
        
        return pe_data
    
    def analyze_hsgt_changes(self, days: int = 5) -> Dict:
        """分析港股通持股变化"""
        hsgt_data = {}
        
        for stock_code, df in self.stock_data.items():
            if df.empty:
                continue
            
            # 筛选有港股通数据的记录
            hsgt_df = df[df['hsgt_holding_ratio'].notna()].copy()
            if len(hsgt_df) < days:
                continue
            
            # 获取最近几日的港股通数据
            recent_hsgt = hsgt_df.tail(days + 1)
            if len(recent_hsgt) >= 2:
                latest_ratio = recent_hsgt['hsgt_holding_ratio'].iloc[-1]
                past_ratio = recent_hsgt['hsgt_holding_ratio'].iloc[0]
                
                ratio_change = latest_ratio - past_ratio
                
                hsgt_data[stock_code] = {
                    'latest_hsgt_ratio': latest_ratio,
                    f'hsgt_change_{days}d': ratio_change,
                    'latest_hsgt_value': recent_hsgt['hsgt_holding_value'].iloc[-1],
                    'hsgt_dates': recent_hsgt['time_key'].tolist()
                }
        
        return hsgt_data
    
    def calculate_portfolio_weights(self) -> Dict:
        """计算投资组合权重（优先使用实时价格）"""
        if self.portfolio_data is None:
            return {}, 0

        # 尝试获取实时价格
        real_time_data = self.get_real_time_prices() if self.use_real_time_prices else {}

        portfolio_values = {}
        total_value = 0
        price_source = "实时价格" if real_time_data else "历史价格"

        print(f"💰 使用{price_source}计算投资组合价值...")

        for _, row in self.portfolio_data.iterrows():
            stock_code = row['code_clean']
            shares = row['shares_held']

            # 优先使用实时价格，否则使用历史价格
            if stock_code in real_time_data:
                price_info = real_time_data[stock_code]
                price = price_info['current_price']
                stock_name = price_info['stock_name']
                change_rate = price_info.get('change_rate', 0)
                update_time = price_info.get('update_time', '')
                data_source = 'real_time'
            elif stock_code in self.latest_prices:
                price_info = self.latest_prices[stock_code]
                price = price_info['latest_price']
                stock_name = price_info['stock_name']
                change_rate = price_info.get('change_rate', 0)
                update_time = str(price_info.get('latest_date', ''))
                data_source = 'historical'
            else:
                print(f"⚠️  未找到 {stock_code} 的价格数据")
                continue

            value = shares * price
            portfolio_values[stock_code] = {
                'shares': shares,
                'price': price,
                'value': value,
                'stock_name': stock_name,
                'change_rate': change_rate,
                'update_time': update_time,
                'data_source': data_source
            }
            total_value += value

            print(f"  {stock_code} ({stock_name}): {shares:,} 股 × {price:.2f} = {value:,.0f} 港币 ({data_source})")

        # 计算权重
        for stock_code in portfolio_values:
            portfolio_values[stock_code]['weight'] = portfolio_values[stock_code]['value'] / total_value * 100

        print(f"📊 投资组合总价值: {total_value:,.2f} 港币 (数据源: {price_source})")

        return portfolio_values, total_value
    
    def calculate_correlation_matrix(self) -> pd.DataFrame:
        """计算股票间相关性矩阵"""
        # 准备价格数据
        price_data = {}
        
        for stock_code, df in self.stock_data.items():
            if len(df) >= 60:  # 至少60天数据
                price_data[stock_code] = df.set_index('time_key')['close']
        
        if len(price_data) < 2:
            return pd.DataFrame()
        
        # 对齐日期并计算收益率
        price_df = pd.DataFrame(price_data)
        price_df = price_df.dropna()
        
        if len(price_df) < 30:  # 至少30个共同交易日
            return pd.DataFrame()
        
        # 计算日收益率
        returns_df = price_df.pct_change().dropna()
        
        # 计算相关性矩阵
        correlation_matrix = returns_df.corr()
        
        return correlation_matrix

    def generate_comprehensive_report(self) -> str:
        """生成综合投资组合报告"""
        print("🔄 开始生成投资组合分析报告...")

        # 1. 加载数据
        if self.load_portfolio() is None:
            return "❌ 无法加载投资组合数据"

        self.load_stock_data_for_portfolio()
        self.get_latest_prices_and_info()

        # 2. 计算各项指标
        returns_data = self.calculate_returns_and_volatility()
        pe_data = self.calculate_pe_percentiles()
        hsgt_data = self.analyze_hsgt_changes()
        portfolio_values, total_value = self.calculate_portfolio_weights()
        correlation_matrix = self.calculate_correlation_matrix()

        # 3. 生成报告
        report = self._format_report(
            portfolio_values, total_value, returns_data,
            pe_data, hsgt_data, correlation_matrix
        )

        return report

    def _format_report(self, portfolio_values, total_value, returns_data,
                      pe_data, hsgt_data, correlation_matrix) -> str:
        """格式化报告内容"""

        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("📊 投资组合分析报告")
        report_lines.append("=" * 80)
        report_lines.append(f"📅 报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append(f"💰 投资组合总价值: {total_value:,.2f} 港币")
        report_lines.append("")

        # 1. 持仓明细和权重
        report_lines.append("📈 持仓明细与权重分布")
        report_lines.append("-" * 60)
        report_lines.append(f"{'股票代码':<10} {'股票名称':<15} {'持股数量':<12} {'最新价格':<10} {'持仓金额':<15} {'权重%':<8}")
        report_lines.append("-" * 60)

        for stock_code, info in portfolio_values.items():
            report_lines.append(
                f"{stock_code:<10} {info['stock_name'][:12]:<15} "
                f"{info['shares']:>12,} {info['price']:>10.2f} "
                f"{info['value']:>15,.0f} {info['weight']:>7.1f}%"
            )

        report_lines.append("")

        # 2. 收益率分析
        report_lines.append("📊 多周期收益率分析")
        report_lines.append("-" * 95)
        report_lines.append(f"{'股票代码':<10} {'公司名称':<15} {'5日%':<8} {'20日%':<8} {'60日%':<8} {'200日%':<8} {'当前波动率%':<12} {'波动率分位%':<12}")
        report_lines.append("-" * 95)

        for stock_code in portfolio_values.keys():
            if stock_code in returns_data:
                data = returns_data[stock_code]
                stock_name = portfolio_values[stock_code]['stock_name'][:12]  # 限制长度
                report_lines.append(
                    f"{stock_code:<10} "
                    f"{stock_name:<15} "
                    f"{data.get('return_5d', 0):>7.1f} "
                    f"{data.get('return_20d', 0):>7.1f} "
                    f"{data.get('return_60d', 0):>7.1f} "
                    f"{data.get('return_200d', 0):>7.1f} "
                    f"{data.get('current_volatility', 0):>11.1f} "
                    f"{data.get('volatility_percentile', 0):>11.1f}"
                )

        report_lines.append("")

        # 3. PE分析
        report_lines.append("💹 PE估值分析")
        report_lines.append("-" * 75)
        report_lines.append(f"{'股票代码':<10} {'公司名称':<15} {'当前PE':<10} {'PE分位数%':<12} {'历史PE区间':<20}")
        report_lines.append("-" * 75)

        for stock_code in portfolio_values.keys():
            if stock_code in pe_data:
                data = pe_data[stock_code]
                stock_name = portfolio_values[stock_code]['stock_name'][:12]  # 限制长度
                pe_range = f"{data['pe_min']:.1f}-{data['pe_max']:.1f}"
                report_lines.append(
                    f"{stock_code:<10} {stock_name:<15} {data['current_pe']:>9.1f} "
                    f"{data['pe_percentile']:>11.1f} {pe_range:<20}"
                )

        report_lines.append("")

        # 4. 港股通分析
        if hsgt_data:
            report_lines.append("🌏 港股通持股变化分析（最近5个交易日）")
            report_lines.append("-" * 85)
            report_lines.append(f"{'股票代码':<10} {'公司名称':<15} {'当前持股比例%':<15} {'5日变化':<12} {'持股市值(万)':<15}")
            report_lines.append("-" * 85)

            for stock_code in portfolio_values.keys():
                if stock_code in hsgt_data:
                    data = hsgt_data[stock_code]
                    stock_name = portfolio_values[stock_code]['stock_name'][:12]  # 限制长度
                    market_value = data['latest_hsgt_value'] / 10000 if data['latest_hsgt_value'] else 0
                    report_lines.append(
                        f"{stock_code:<10} {stock_name:<15} {data['latest_hsgt_ratio']:>14.2f} "
                        f"{data['hsgt_change_5d']:>11.2f} {market_value:>14.0f}"
                    )
            report_lines.append("")

        # 5. 相关性分析
        if not correlation_matrix.empty and len(correlation_matrix) > 1:
            report_lines.append("🔗 股票间相关性矩阵")
            report_lines.append("-" * 60)

            # 显示相关性矩阵
            corr_str = correlation_matrix.round(3).to_string()
            report_lines.append(corr_str)
            report_lines.append("")

            # 找出最高和最低相关性
            corr_values = []
            for i in range(len(correlation_matrix)):
                for j in range(i+1, len(correlation_matrix)):
                    stock1 = correlation_matrix.index[i]
                    stock2 = correlation_matrix.index[j]
                    corr_val = correlation_matrix.iloc[i, j]
                    corr_values.append((stock1, stock2, corr_val))

            if corr_values:
                corr_values.sort(key=lambda x: abs(x[2]), reverse=True)
                report_lines.append("🔍 关键相关性发现:")
                for stock1, stock2, corr in corr_values[:3]:  # 显示前3个最强相关性
                    corr_type = "正相关" if corr > 0 else "负相关"
                    report_lines.append(f"  {stock1} ↔ {stock2}: {corr:.3f} ({corr_type})")

        report_lines.append("")
        report_lines.append("=" * 80)
        report_lines.append("📝 报告说明:")
        report_lines.append("• 收益率为相对于N个交易日前的涨跌幅")
        report_lines.append("• 波动率为年化波动率，基于最近20个交易日计算")
        report_lines.append("• PE分位数表示当前PE在历史PE分布中的位置")
        report_lines.append("• 港股通数据来源于最新可获得的交易日")
        report_lines.append("• 相关性基于日收益率计算")
        report_lines.append("=" * 80)

        return "\n".join(report_lines)

    def save_report_to_file(self, report_content: str, filename: str = None) -> str:
        """保存报告到文件"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"portfolio_report_{timestamp}.txt"

        filepath = os.path.join(os.path.dirname(self.portfolio_file), filename)

        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(report_content)
            print(f"✅ 报告已保存到: {filepath}")
            return filepath
        except Exception as e:
            print(f"❌ 保存报告失败: {e}")
            return ""


def main(use_real_time_prices: bool = True):
    """
    主函数

    Args:
        use_real_time_prices: 是否使用实时价格计算投资组合价值
    """
    # 获取当前脚本所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    portfolio_file = os.path.join(current_dir, "portfolio_list_1")

    # 检查投资组合文件是否存在
    if not os.path.exists(portfolio_file):
        print(f"❌ 投资组合文件不存在: {portfolio_file}")
        return

    print(f"🔄 开始生成投资组合分析报告...")
    print(f"💰 价格数据源: {'实时价格 (富途API)' if use_real_time_prices else '历史价格 (CSV文件)'}")

    # 创建分析器
    analyzer = PortfolioAnalyzer(portfolio_file, use_real_time_prices=use_real_time_prices)

    # 生成报告
    report = analyzer.generate_comprehensive_report()

    # 显示报告
    print("\n" + report)

    # 保存报告
    report_file = analyzer.save_report_to_file(report)

    return report_file


if __name__ == "__main__":
    main()
