#!/bin/bash

# 投资组合分析报告生成脚本
# 使用方法: ./run_analysis.sh [real_time|historical]

echo "🚀 启动投资组合分析系统..."

# 切换到项目目录
cd "$(dirname "$0")/.."

# 激活虚拟环境
if [ -d ".venv" ]; then
    echo "📦 激活虚拟环境..."
    source .venv/bin/activate
else
    echo "⚠️  虚拟环境不存在，使用系统Python"
fi

# 检查参数
MODE=${1:-"real_time"}

if [ "$MODE" = "historical" ]; then
    echo "📊 使用历史价格模式..."
    python -c "
from portfolio_monitor.portfolio_analysis_report import main
main(use_real_time_prices=False)
"
else
    echo "💰 使用实时价格模式..."
    echo "📡 请确保富途牛牛已启动并开启API接口"
    python portfolio_monitor/portfolio_analysis_report.py
fi

echo ""
echo "✅ 分析完成！"
echo "📁 查看生成的文件:"
echo "   - 文本报告: portfolio_monitor/portfolio_report_*.txt"
echo "   - HTML报告: portfolio_monitor/portfolio_report_*.html"
echo "   - 图表文件: portfolio_monitor/charts/"
echo ""
echo "💡 使用说明:"
echo "   ./run_analysis.sh           # 使用实时价格（推荐）"
echo "   ./run_analysis.sh historical # 使用历史价格（离线模式）"
