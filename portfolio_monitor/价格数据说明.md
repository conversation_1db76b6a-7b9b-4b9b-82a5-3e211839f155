# 投资组合分析系统 - 价格数据说明

## 📊 关于长城汽车价格的说明

### 🔍 问题背景
您提到长城汽车的价格看起来很高（359.76港币），这是因为我们使用的是**后复权价格数据**。

### 📈 后复权 vs 前复权价格

#### 后复权价格（用于分析）
- **当前使用**: 359.76 港币
- **用途**: 技术分析、收益率计算、趋势分析
- **优点**: 保持价格连续性，适合计算历史收益率
- **数据源**: `data/H_daily/*.csv` 文件

#### 前复权价格（实际价格）
- **实际价格**: 约 10-20 港币（需要通过富途API获取）
- **用途**: 计算投资组合实际价值
- **优点**: 反映真实市场价格
- **数据源**: 富途API实时数据

### 🔧 解决方案

我们已经实现了**双重价格系统**：

1. **历史分析使用后复权价格**
   - 收益率计算
   - 波动率分析
   - 技术指标计算

2. **投资组合价值使用实时前复权价格**
   - 通过富途API获取实时价格
   - 计算真实投资组合价值
   - 显示实际持仓金额

## 🚀 如何启用实时价格功能

### 1. 启动富途牛牛客户端
```bash
# 确保富途牛牛已启动并开启API接口
# 默认端口: 11111
```

### 2. 使用实时价格生成报告
```python
# 方法1: 直接运行（默认启用实时价格）
python portfolio_monitor/portfolio_analysis_report.py

# 方法2: 在代码中指定
from portfolio_monitor.portfolio_analysis_report import main
main(use_real_time_prices=True)  # 使用实时价格
main(use_real_time_prices=False) # 使用历史价格
```

### 3. 测试实时价格获取
```python
# 测试实时价格功能
python portfolio_monitor/real_time_price_fetcher.py
```

## 📋 当前报告改进

### ✅ 已完成的改进

1. **添加公司名称列**
   - PE估值分析表格
   - 多周期收益率分析表格  
   - 港股通持股变化分析表格

2. **修复字体乱码问题**
   - 使用 `utilities/complete_font_solution.py` 中的字体解决方案
   - 图表中文显示正常

3. **图表优化**
   - 所有图表使用股票名称而不是代码
   - 改进标签显示和布局

4. **实时价格支持**
   - 创建 `RealTimePriceFetcher` 类
   - 支持批量获取实时价格
   - 自动回退到历史价格

### 📊 报告内容对比

| 分析项目 | 数据源 | 说明 |
|---------|--------|------|
| 持仓金额和权重 | 实时价格（如可用） | 反映真实投资组合价值 |
| 收益率分析 | 后复权历史价格 | 保证计算准确性 |
| PE估值分析 | 历史数据 | 基于最新财务数据 |
| 波动率分析 | 后复权历史价格 | 技术分析标准做法 |
| 港股通分析 | 历史数据 | 最新可获得数据 |
| 相关性分析 | 后复权历史价格 | 统计分析标准 |

## 🔄 使用流程

### 日常使用（推荐）
1. 启动富途牛牛客户端
2. 运行分析脚本（自动使用实时价格）
3. 查看HTML报告

### 离线使用
1. 直接运行脚本（自动回退到历史价格）
2. 价值计算基于最新历史数据

## 📁 文件结构

```
portfolio_monitor/
├── portfolio_analysis_report.py     # 主分析脚本
├── real_time_price_fetcher.py      # 实时价格获取器
├── portfolio_visualization.py      # 图表生成器
├── generate_html_report.py         # HTML报告生成器
├── data_validation.py              # 数据验证工具
├── portfolio_list_1                # 投资组合配置
├── charts/                         # 生成的图表
│   ├── portfolio_weights.png
│   ├── returns_comparison.png
│   ├── risk_return_scatter.png
│   ├── correlation_heatmap.png
│   └── volatility_analysis.png
└── portfolio_report_*.html         # 生成的HTML报告
```

## ⚠️ 注意事项

1. **富途API限制**
   - 需要富途牛牛客户端运行
   - 有API调用频率限制
   - 需要港股行情权限

2. **数据一致性**
   - 实时价格用于价值计算
   - 历史价格用于趋势分析
   - 两者不应混用

3. **时间同步**
   - 实时价格反映当前市场
   - 历史数据可能有延迟

## 🎯 下一步优化建议

1. **增加更多实时数据**
   - 实时PE比率
   - 实时成交量
   - 实时涨跌幅

2. **数据缓存机制**
   - 避免频繁API调用
   - 提高响应速度

3. **自动化报告**
   - 定时生成报告
   - 邮件推送功能

---

*最后更新: 2025年9月4日*
*如有问题，请检查富途API连接状态*
